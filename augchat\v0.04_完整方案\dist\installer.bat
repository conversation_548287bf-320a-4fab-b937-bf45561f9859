@echo off
setlocal EnableDelayedExpansion

echo ========================================
echo   Augment 聊天记录导出器 v1.0.0
echo   自解压安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ⚠️  建议以管理员身份运行以获得最佳体验
)

:: 设置安装路径
set "INSTALL_DIR=%ProgramFiles%\AugmentExporter"
set "USER_INSTALL_DIR=%USERPROFILE%\AppData\Local\AugmentExporter"

echo.
echo 选择安装位置:
echo [1] 系统目录 (需要管理员权限): %INSTALL_DIR%
echo [2] 用户目录: %USER_INSTALL_DIR%
echo.
set /p choice="请选择 (1 或 2): "

if "%choice%"=="1" (
    set "TARGET_DIR=%INSTALL_DIR%"
    set "ADD_TO_PATH=1"
) else (
    set "TARGET_DIR=%USER_INSTALL_DIR%"
    set "ADD_TO_PATH=0"
)

echo.
echo 正在安装到: %TARGET_DIR%
echo.

:: 创建目录
if not exist "%TARGET_DIR%" (
    mkdir "%TARGET_DIR%" 2>nul
    if !errorLevel! neq 0 (
        echo ❌ 无法创建目录，请以管理员身份运行
        pause
        exit /b 1
    )
)

:: 提取文件（这里需要实际的文件提取逻辑）
echo 正在提取文件...
copy "%~dp0augment-chat-exporter.obf.exe" "%TARGET_DIR%\augment-exporter.exe" >nul

:: 添加到系统路径
if "%ADD_TO_PATH%"=="1" (
    echo 正在添加到系统路径...
    setx PATH "%PATH%;%TARGET_DIR%" /M >nul 2>&1
) else (
    echo 正在添加到用户路径...
    setx PATH "%PATH%;%TARGET_DIR%" >nul 2>&1
)

:: 创建桌面快捷方式
echo 正在创建桌面快捷方式...
powershell -Command "\$WshShell = New-Object -comObject WScript.Shell; \$Shortcut = \$WshShell.CreateShortcut('\$env:USERPROFILE\Desktop\Augment导出器.lnk'); \$Shortcut.TargetPath = '%TARGET_DIR%\augment-exporter.exe'; \$Shortcut.Save()"

echo.
echo ✅ 安装完成！
echo.
echo 使用方法:
echo   - 桌面快捷方式: Augment导出器
echo   - 命令行: augment-exporter
echo   - 直接运行: "%TARGET_DIR%\augment-exporter.exe"
echo.
echo 按任意键退出...
pause >nul
