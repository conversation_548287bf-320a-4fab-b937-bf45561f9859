// 主题配置
const themes = {
    default: {
        theme: 'default',
        themeVariables: {}
    },
    dark: {
        theme: 'dark',
        themeVariables: {}
    },
    forest: {
        theme: 'forest',
        themeVariables: {}
    },
    neutral: {
        theme: 'neutral',
        themeVariables: {}
    },
    custom1: {
        theme: 'base',
        themeVariables: {
            primaryColor: '#8B5CF6',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#7C3AED',
            lineColor: '#4C1D95',
            sectionBkgColor: '#F3E8FF',
            altSectionBkgColor: '#E9D5FF',
            gridColor: '#C4B5FD',
            cScale0: '#8B5CF6',
            cScale1: '#A78BFA',
            cScale2: '#C4B5FD',
            cScale3: '#DDD6FE',
            cScale4: '#EDE9FE'
        }
    },
    custom2: {
        theme: 'base',
        themeVariables: {
            primaryColor: '#F97316',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#EA580C',
            lineColor: '#9A3412',
            sectionBkgColor: '#FFF7ED',
            altSectionBkgColor: '#FFEDD5',
            gridColor: '#FED7AA',
            cScale0: '#F97316',
            cScale1: '#FB923C',
            cScale2: '#FDBA74',
            cScale3: '#FED7AA',
            cScale4: '#FFEDD5'
        }
    },
    custom3: {
        theme: 'base',
        themeVariables: {
            primaryColor: '#06B6D4',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#0891B2',
            lineColor: '#164E63',
            sectionBkgColor: '#F0F9FF',
            altSectionBkgColor: '#E0F7FA',
            gridColor: '#B2EBF2',
            cScale0: '#06B6D4',
            cScale1: '#22D3EE',
            cScale2: '#67E8F9',
            cScale3: '#A7F3D0',
            cScale4: '#CCFBF1'
        }
    }
};

// 甘特图内容
const ganttContent = `
gantt
    title 2025 年度工作安排（甘特图）
    dateFormat  YYYY-MM-DD
    axisFormat  %Y-%m
    excludes    weekends

    section 基础建设
    后端基础框架搭建      :done,    backend,  2025-01-10, 110d
    前端基础框架融合      :active,  frontend, 2025-02-10, 140d

    section 能力与服务
    知识库管理            :active,  kb,       2025-04-01, 150d
    MCP服务管理           :         mcp,      2025-05-01, 140d
    提示词模板管理        :         prompt,   2025-06-01, 120d

    section 智能体与交互
    智能体配置            :         aicfg,    2025-03-01, 130d
    多智能体协作编排      :crit,    multi,    2025-07-01, 110d
    Live2D数字人交互      :         live2d,   2025-08-15, 120d

    section 里程碑
    上半年目标 50%（评审通过） :milestone, m1, 2025-06-30, 0d
    下半年目标 100%（上线验收）:milestone, m2, 2025-12-20, 0d
`;

// 初始化函数
function initializeMermaid(themeName = 'default') {
    const themeConfig = themes[themeName];

    // 清除之前的图表
    const chartElement = document.getElementById('gantt-chart');
    chartElement.innerHTML = ganttContent.trim();
    chartElement.removeAttribute('data-processed');

    // 重新初始化 Mermaid
    mermaid.initialize({
        startOnLoad: false,
        ...themeConfig,
        gantt: {
            fontSize: 12,
            fontFamily: 'Microsoft YaHei, sans-serif',
            gridLineStartPadding: 350,
            sectionFontSize: 16,
            numberSectionStyles: 4,
            axisFormat: '%m月',
            bottomPadding: 50,
            rightPadding: 100,
            leftPadding: 100,
            topPadding: 50,
            barHeight: 20,
            gridLineStartPadding: 350
        }
    });

    // 渲染图表
    mermaid.run();
}

// 主题切换函数
function changeTheme(themeName) {
    // 添加过渡效果
    const chartContainer = document.querySelector('.chart-container');
    chartContainer.style.opacity = '0.5';
    chartContainer.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        initializeMermaid(themeName);
        
        // 恢复样式
        setTimeout(() => {
            chartContainer.style.opacity = '1';
            chartContainer.style.transform = 'scale(1)';
        }, 100);
    }, 200);
    
    // 保存用户选择
    localStorage.setItem('selectedTheme', themeName);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取保存的主题或使用默认主题
    const savedTheme = localStorage.getItem('selectedTheme') || 'default';
    
    // 设置选择器的值
    const themeSelect = document.getElementById('theme-select');
    themeSelect.value = savedTheme;
    
    // 初始化图表
    initializeMermaid(savedTheme);
    
    // 添加主题切换事件监听器
    themeSelect.addEventListener('change', function() {
        changeTheme(this.value);
    });
    
    // 添加图表点击事件（可选）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.mermaid')) {
            console.log('图表被点击');
            // 这里可以添加更多交互功能
        }
    });
});

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case '1':
                e.preventDefault();
                document.getElementById('theme-select').value = 'default';
                changeTheme('default');
                break;
            case '2':
                e.preventDefault();
                document.getElementById('theme-select').value = 'dark';
                changeTheme('dark');
                break;
            case '3':
                e.preventDefault();
                document.getElementById('theme-select').value = 'forest';
                changeTheme('forest');
                break;
        }
    }
});

// 导出功能（可选）
function exportChart() {
    const svg = document.querySelector('#gantt-chart svg');
    if (svg) {
        const svgData = new XMLSerializer().serializeToString(svg);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            const link = document.createElement('a');
            link.download = '甘特图_2025年度工作安排.png';
            link.href = canvas.toDataURL();
            link.click();
        };
        
        img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化甘特图...');

    // 初始化主题选择器
    const themeSelect = document.getElementById('theme-select');
    if (themeSelect) {
        themeSelect.addEventListener('change', function() {
            console.log('切换主题到:', this.value);
            initializeMermaid(this.value);
        });
    }

    // 初始化默认甘特图
    setTimeout(() => {
        initializeMermaid('default');
    }, 100);
});
