#!/usr/bin/env node

/**
 * 高级加密构建脚本
 * 使用多种技术保护可执行文件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

console.log('🔐 高级加密构建 Augment 聊天记录导出器...\n');

// 配置
const CONFIG = {
    // UPX 压缩（减小文件大小并增加逆向难度）
    useUPX: true,
    // 代码混淆
    useObfuscation: true,
    // 自定义图标
    useCustomIcon: true,
    // 数字签名（需要证书）
    useCodeSigning: false
};

// 检查工具
function checkTools() {
    console.log('🔧 检查构建工具...');
    
    const tools = [
        { name: 'pkg', command: 'pkg --version', install: 'npm install -g pkg' },
        { name: 'upx', command: 'upx --version', install: '下载 https://upx.github.io/' }
    ];
    
    for (const tool of tools) {
        try {
            execSync(tool.command, { stdio: 'pipe' });
            console.log(`✅ ${tool.name} 已安装`);
        } catch (error) {
            if (tool.name === 'pkg') {
                console.log(`❌ ${tool.name} 未安装，正在安装...`);
                execSync(tool.install, { stdio: 'inherit' });
                console.log(`✅ ${tool.name} 安装完成`);
            } else {
                console.log(`⚠️  ${tool.name} 未安装: ${tool.install}`);
                CONFIG.useUPX = false;
            }
        }
    }
    console.log();
}

// 创建混淆版本的源码
function createObfuscatedSource() {
    if (!CONFIG.useObfuscation) return 'augment-chat-exporter.js';
    
    console.log('🔀 创建代码混淆版本...');
    
    const originalFile = 'augment-chat-exporter.js';
    const obfuscatedFile = 'augment-chat-exporter.obf.js';
    
    let sourceCode = fs.readFileSync(originalFile, 'utf8');
    
    // 简单的变量名混淆
    const varMap = new Map();
    let varCounter = 0;
    
    // 混淆函数名和变量名
    sourceCode = sourceCode.replace(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g, (match, name) => {
        if (!varMap.has(name)) {
            varMap.set(name, `_0x${varCounter.toString(16)}`);
            varCounter++;
        }
        return `function ${varMap.get(name)}`;
    });
    
    // 混淆字符串（简单版本）
    sourceCode = sourceCode.replace(/'([^'\\\\]|\\\\.)*'/g, (match) => {
        const str = match.slice(1, -1);
        const encoded = Buffer.from(str).toString('base64');
        return `Buffer.from('${encoded}', 'base64').toString()`;
    });
    
    // 添加反调试代码
    const antiDebugCode = `
// Anti-debug measures
(function() {
    const start = Date.now();
    debugger;
    if (Date.now() - start > 100) {
        process.exit(1);
    }
})();

// Check for common debugging tools
if (process.env.NODE_ENV === 'development' || 
    process.argv.includes('--inspect') || 
    process.argv.includes('--debug')) {
    process.exit(1);
}
`;
    
    sourceCode = antiDebugCode + sourceCode;
    
    fs.writeFileSync(obfuscatedFile, sourceCode);
    console.log('✅ 代码混淆完成');
    
    return obfuscatedFile;
}

// 创建自定义图标
function createIcon() {
    if (!CONFIG.useCustomIcon) return null;
    
    console.log('🎨 创建自定义图标...');
    
    // 这里应该有一个 .ico 文件
    // 为了演示，我们创建一个简单的图标描述
    const iconPath = path.join(__dirname, 'app.ico');
    
    if (!fs.existsSync(iconPath)) {
        console.log('⚠️  未找到 app.ico，跳过图标设置');
        return null;
    }
    
    return iconPath;
}

// 构建可执行文件
function buildExecutable(sourceFile, iconPath) {
    console.log('🔨 构建可执行文件...');
    
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }
    
    let command = `pkg ${sourceFile} --targets=node18-win-x64 --out-path=dist --compress=Brotli`;
    
    // 添加图标
    if (iconPath) {
        command += ` --icon="${iconPath}"`;
    }
    
    // 添加版本信息
    command += ` --options="--title=Augment Chat Exporter,--description=VSCode Augment聊天记录导出工具,--version=1.0.0"`;
    
    try {
        console.log(`执行命令: ${command}`);
        execSync(command, { stdio: 'inherit', cwd: __dirname });
        
        const exePath = path.join(distDir, path.basename(sourceFile, '.js') + '.exe');
        console.log('✅ 可执行文件构建完成');
        return exePath;
    } catch (error) {
        console.error('❌ 构建失败:', error.message);
        process.exit(1);
    }
}

// UPX 压缩
function compressWithUPX(exePath) {
    if (!CONFIG.useUPX) return exePath;
    
    console.log('📦 使用 UPX 压缩...');
    
    try {
        const compressedPath = exePath.replace('.exe', '-compressed.exe');
        execSync(`upx --best --lzma "${exePath}" -o "${compressedPath}"`, { stdio: 'inherit' });
        console.log('✅ UPX 压缩完成');
        return compressedPath;
    } catch (error) {
        console.log('⚠️  UPX 压缩失败，使用原文件');
        return exePath;
    }
}

// 创建自解压安装包
function createSelfExtractingInstaller(exePath) {
    console.log('📦 创建自解压安装包...');
    
    const installerScript = `@echo off
setlocal EnableDelayedExpansion

echo ========================================
echo   Augment 聊天记录导出器 v1.0.0
echo   自解压安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ⚠️  建议以管理员身份运行以获得最佳体验
)

:: 设置安装路径
set "INSTALL_DIR=%ProgramFiles%\\AugmentExporter"
set "USER_INSTALL_DIR=%USERPROFILE%\\AppData\\Local\\AugmentExporter"

echo.
echo 选择安装位置:
echo [1] 系统目录 (需要管理员权限): %INSTALL_DIR%
echo [2] 用户目录: %USER_INSTALL_DIR%
echo.
set /p choice="请选择 (1 或 2): "

if "%choice%"=="1" (
    set "TARGET_DIR=%INSTALL_DIR%"
    set "ADD_TO_PATH=1"
) else (
    set "TARGET_DIR=%USER_INSTALL_DIR%"
    set "ADD_TO_PATH=0"
)

echo.
echo 正在安装到: %TARGET_DIR%
echo.

:: 创建目录
if not exist "%TARGET_DIR%" (
    mkdir "%TARGET_DIR%" 2>nul
    if !errorLevel! neq 0 (
        echo ❌ 无法创建目录，请以管理员身份运行
        pause
        exit /b 1
    )
)

:: 提取文件（这里需要实际的文件提取逻辑）
echo 正在提取文件...
copy "%~dp0${path.basename(exePath)}" "%TARGET_DIR%\\augment-exporter.exe" >nul

:: 添加到系统路径
if "%ADD_TO_PATH%"=="1" (
    echo 正在添加到系统路径...
    setx PATH "%PATH%;%TARGET_DIR%" /M >nul 2>&1
) else (
    echo 正在添加到用户路径...
    setx PATH "%PATH%;%TARGET_DIR%" >nul 2>&1
)

:: 创建桌面快捷方式
echo 正在创建桌面快捷方式...
powershell -Command "\\$WshShell = New-Object -comObject WScript.Shell; \\$Shortcut = \\$WshShell.CreateShortcut('\\$env:USERPROFILE\\Desktop\\Augment导出器.lnk'); \\$Shortcut.TargetPath = '%TARGET_DIR%\\augment-exporter.exe'; \\$Shortcut.Save()"

echo.
echo ✅ 安装完成！
echo.
echo 使用方法:
echo   - 桌面快捷方式: Augment导出器
echo   - 命令行: augment-exporter
echo   - 直接运行: "%TARGET_DIR%\\augment-exporter.exe"
echo.
echo 按任意键退出...
pause >nul
`;

    const installerPath = path.join(path.dirname(exePath), 'installer.bat');
    fs.writeFileSync(installerPath, installerScript);
    console.log('✅ 自解压安装包已创建');
}

// 清理临时文件
function cleanup() {
    console.log('🧹 清理临时文件...');
    
    const tempFiles = [
        'augment-chat-exporter.obf.js'
    ];
    
    tempFiles.forEach(file => {
        if (fs.existsSync(file)) {
            fs.unlinkSync(file);
            console.log(`🗑️  删除: ${file}`);
        }
    });
}

// 主函数
function main() {
    try {
        checkTools();
        
        const sourceFile = createObfuscatedSource();
        const iconPath = createIcon();
        const exePath = buildExecutable(sourceFile, iconPath);
        const finalPath = compressWithUPX(exePath);
        
        createSelfExtractingInstaller(finalPath);
        
        // 创建发布包
        const releaseDir = path.join(__dirname, 'release');
        if (!fs.existsSync(releaseDir)) {
            fs.mkdirSync(releaseDir);
        }
        
        // 复制最终文件到发布目录
        const releaseName = 'AugmentChatExporter-v1.0.0.exe';
        fs.copyFileSync(finalPath, path.join(releaseDir, releaseName));
        fs.copyFileSync(
            path.join(path.dirname(finalPath), 'installer.bat'),
            path.join(releaseDir, 'installer.bat')
        );
        
        cleanup();
        
        console.log('\n🎉 高级加密构建完成！');
        console.log('\n📁 发布文件:');
        console.log(`   - release/${releaseName}`);
        console.log(`   - release/installer.bat`);
        
        console.log('\n🔐 安全特性:');
        console.log('   ✅ 代码混淆');
        console.log('   ✅ 反调试保护');
        console.log('   ✅ 字符串编码');
        if (CONFIG.useUPX) console.log('   ✅ UPX 压缩');
        console.log('   ✅ 自解压安装');
        
        console.log('\n💡 分享方式:');
        console.log('   1. 单文件分享: AugmentChatExporter-v1.0.0.exe');
        console.log('   2. 完整包分享: release 文件夹');
        
    } catch (error) {
        console.error('❌ 构建过程出错:', error.message);
        cleanup();
        process.exit(1);
    }
}

// 运行
if (require.main === module) {
    main();
}

module.exports = { main };
