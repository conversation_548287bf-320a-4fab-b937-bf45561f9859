# 🔐 Augment 聊天记录导出器 - 可执行文件构建指南

## 📋 概述

将 Node.js 脚本封装成加密的 .exe 文件，方便分享和使用，无需目标机器安装 Node.js 环境。

## 🛠️ 构建方案

### 方案一：基础构建（推荐新手）
- ✅ 简单快速
- ✅ 文件压缩
- ✅ 基础保护
- ⚡ 构建时间：1-2分钟

### 方案二：高级加密构建（推荐分享）
- ✅ 代码混淆
- ✅ 反调试保护
- ✅ 字符串编码
- ✅ UPX 压缩
- ✅ 自解压安装
- ⚡ 构建时间：3-5分钟

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保已安装 Node.js (推荐 v18+)
node --version

# 进入项目目录
cd augchat/v0.04_完整方案
```

### 2. 一键构建
```bash
# Windows 用户
build.bat

# 或手动执行
npm install
node build-exe.js        # 基础构建
node advanced-build.js   # 高级构建
```

### 3. 获取结果
构建完成后，在 `dist/` 或 `release/` 目录找到可执行文件。

## 📦 构建工具详解

### PKG - Node.js 打包工具
```bash
# 安装
npm install -g pkg

# 基本用法
pkg augment-chat-exporter.js --targets=node18-win-x64 --out-path=dist

# 高级选项
pkg script.js --targets=node18-win-x64 --compress=Brotli --icon=app.ico
```

### UPX - 可执行文件压缩器
```bash
# 下载地址: https://upx.github.io/
# 使用方法
upx --best --lzma input.exe -o output.exe
```

## 🔐 加密保护技术

### 1. 代码混淆
- 变量名随机化
- 函数名编码
- 字符串 Base64 编码
- 控制流混淆

### 2. 反调试保护
```javascript
// 时间检测
const start = Date.now();
debugger;
if (Date.now() - start > 100) {
    process.exit(1);
}

// 环境检测
if (process.env.NODE_ENV === 'development') {
    process.exit(1);
}
```

### 3. 文件保护
- UPX 压缩（减小体积 + 增加逆向难度）
- 自定义图标和版本信息
- 数字签名（可选）

## 📁 输出文件说明

### 基础构建输出
```
dist/
├── augment-chat-exporter.exe          # 主程序
├── augment-chat-exporter-protected.exe # 保护版本
├── install.bat                         # 安装脚本
└── README.md                          # 使用说明
```

### 高级构建输出
```
release/
├── AugmentChatExporter-v1.0.0.exe     # 最终发布版本
└── installer.bat                       # 自解压安装程序
```

## 🎯 分享建议

### 单文件分享
- 分享 `augment-chat-exporter-protected.exe`
- 文件大小约 50MB（包含 Node.js 运行时）
- 双击即可运行，无需安装

### 完整包分享
- 打包整个 `dist/` 或 `release/` 文件夹
- 包含安装脚本和说明文档
- 提供更好的用户体验

## ⚡ 性能优化

### 减小文件大小
```bash
# 使用 Brotli 压缩
pkg script.js --compress=Brotli

# UPX 压缩
upx --best --lzma input.exe

# 排除不必要的模块
pkg script.js --ignore=dev-dependency
```

### 启动速度优化
```javascript
// 延迟加载大模块
const heavyModule = () => require('heavy-module');

// 预编译正则表达式
const regex = /pattern/g;
```

## 🔧 高级配置

### package.json 配置
```json
{
  "pkg": {
    "targets": ["node18-win-x64"],
    "outputPath": "dist",
    "assets": [
      "node_modules/level/**/*"
    ],
    "scripts": {
      "build": "pkg . --out-path=dist"
    }
  }
}
```

### 自定义图标
1. 准备 `.ico` 格式图标文件
2. 使用 `--icon` 参数指定图标路径
3. 推荐尺寸：256x256 或 512x512

### 版本信息
```bash
pkg script.js --options="--title=应用名称,--description=应用描述,--version=1.0.0"
```

## 🛡️ 安全注意事项

### 1. 代码保护限制
- JavaScript 本质上是解释型语言
- 完全的代码保护几乎不可能
- 混淆只能增加逆向难度

### 2. 推荐做法
- 重要逻辑放在服务端
- 使用 HTTPS 通信
- 实现许可证验证
- 定期更新保护机制

### 3. 法律保护
- 添加版权声明
- 使用软件许可协议
- 考虑专利保护

## 🐛 常见问题

### Q: 构建失败怎么办？
A: 检查 Node.js 版本，确保依赖已安装，查看错误日志

### Q: 文件太大怎么办？
A: 使用 UPX 压缩，排除不必要的依赖，考虑在线更新

### Q: 杀毒软件误报怎么办？
A: 添加数字签名，向杀毒厂商申请白名单，使用知名打包工具

### Q: 如何添加自动更新？
A: 实现版本检查机制，提供增量更新，使用 GitHub Releases

## 📚 参考资源

- [PKG 官方文档](https://github.com/vercel/pkg)
- [UPX 官方网站](https://upx.github.io/)
- [Node.js 官方文档](https://nodejs.org/docs/)
- [Electron 替代方案](https://www.electronjs.org/)

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进构建脚本！

---

**构建愉快！** 🎉
