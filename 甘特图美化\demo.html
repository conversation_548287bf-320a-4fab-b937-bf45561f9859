<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图美化 - 演示版</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .theme-controls {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        
        .theme-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .theme-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .theme-btn:active {
            transform: translateY(-1px);
        }
        
        .btn-default { background: linear-gradient(135deg, #3498db, #2980b9); }
        .btn-dark { background: linear-gradient(135deg, #34495e, #2c3e50); }
        .btn-forest { background: linear-gradient(135deg, #27ae60, #229954); }
        .btn-purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .btn-orange { background: linear-gradient(135deg, #f97316, #ea580c); }
        .btn-cyan { background: linear-gradient(135deg, #06b6d4, #0891b2); }
        
        .chart-area {
            padding: 40px;
            background: white;
        }
        
        .mermaid {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            margin: 20px 0;
            border-radius: 10px;
            background: rgba(52, 152, 219, 0.1);
            color: #2980b9;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .theme-controls {
                gap: 10px;
            }
            
            .theme-btn {
                padding: 10px 18px;
                font-size: 12px;
            }
            
            .chart-area {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 2025年度工作安排</h1>
            <div class="theme-controls">
                <button class="theme-btn btn-default" onclick="switchTheme('default')">经典蓝色</button>
                <button class="theme-btn btn-dark" onclick="switchTheme('dark')">深色主题</button>
                <button class="theme-btn btn-forest" onclick="switchTheme('forest')">森林绿色</button>
                <button class="theme-btn btn-purple" onclick="switchTheme('purple')">渐变紫色</button>
                <button class="theme-btn btn-orange" onclick="switchTheme('orange')">温暖橙色</button>
                <button class="theme-btn btn-cyan" onclick="switchTheme('cyan')">清新青色</button>
            </div>
        </div>
        
        <div class="status" id="status">当前主题：经典蓝色</div>
        
        <div class="chart-area">
            <div id="chart-container">
                <div class="loading">正在加载甘特图...</div>
            </div>
        </div>
    </div>

    <script>
        // 甘特图数据
        const ganttData = `gantt
    title 2025 年度工作安排（甘特图）
    dateFormat  YYYY-MM-DD
    axisFormat  %m月
    excludes    weekends

    section 基础建设
    后端基础框架搭建      :done,    backend,  2025-01-10, 110d
    前端基础框架融合      :active,  frontend, 2025-02-10, 140d

    section 能力与服务
    知识库管理            :active,  kb,       2025-04-01, 150d
    MCP服务管理           :         mcp,      2025-05-01, 140d
    提示词模板管理        :         prompt,   2025-06-01, 120d

    section 智能体与交互
    智能体配置            :         aicfg,    2025-03-01, 130d
    多智能体协作编排      :crit,    multi,    2025-07-01, 110d
    Live2D数字人交互      :         live2d,   2025-08-15, 120d

    section 里程碑
    上半年目标 50%（评审通过） :milestone, m1, 2025-06-30, 0d
    下半年目标 100%（上线验收）:milestone, m2, 2025-12-20, 0d`;

        // 主题配置
        const themeConfigs = {
            default: {
                name: '经典蓝色',
                config: { theme: 'default' }
            },
            dark: {
                name: '深色主题',
                config: { theme: 'dark' }
            },
            forest: {
                name: '森林绿色',
                config: { theme: 'forest' }
            },
            purple: {
                name: '渐变紫色',
                config: {
                    theme: 'base',
                    themeVariables: {
                        primaryColor: '#8B5CF6',
                        primaryTextColor: '#ffffff',
                        primaryBorderColor: '#7C3AED',
                        lineColor: '#4C1D95',
                        sectionBkgColor: '#F3E8FF',
                        altSectionBkgColor: '#E9D5FF',
                        gridColor: '#C4B5FD'
                    }
                }
            },
            orange: {
                name: '温暖橙色',
                config: {
                    theme: 'base',
                    themeVariables: {
                        primaryColor: '#F97316',
                        primaryTextColor: '#ffffff',
                        primaryBorderColor: '#EA580C',
                        lineColor: '#9A3412',
                        sectionBkgColor: '#FFF7ED',
                        altSectionBkgColor: '#FFEDD5',
                        gridColor: '#FED7AA'
                    }
                }
            },
            cyan: {
                name: '清新青色',
                config: {
                    theme: 'base',
                    themeVariables: {
                        primaryColor: '#06B6D4',
                        primaryTextColor: '#ffffff',
                        primaryBorderColor: '#0891B2',
                        lineColor: '#164E63',
                        sectionBkgColor: '#F0F9FF',
                        altSectionBkgColor: '#E0F7FA',
                        gridColor: '#B2EBF2'
                    }
                }
            }
        };

        let currentTheme = 'default';

        // 切换主题函数
        function switchTheme(themeName) {
            if (!themeConfigs[themeName]) return;
            
            currentTheme = themeName;
            const themeInfo = themeConfigs[themeName];
            
            // 更新状态显示
            document.getElementById('status').textContent = `当前主题：${themeInfo.name}`;
            
            // 显示加载状态
            const container = document.getElementById('chart-container');
            container.innerHTML = '<div class="loading">正在切换主题...</div>';
            
            // 延迟渲染以显示加载状态
            setTimeout(() => {
                renderChart(themeInfo.config);
            }, 300);
        }

        // 渲染图表函数
        function renderChart(themeConfig) {
            // 创建新的图表容器
            const container = document.getElementById('chart-container');
            const chartId = 'gantt-' + Date.now();
            
            container.innerHTML = `<div class="mermaid" id="${chartId}">${ganttData}</div>`;
            
            // 初始化 Mermaid
            mermaid.initialize({
                startOnLoad: false,
                ...themeConfig
            });
            
            // 渲染图表
            mermaid.run({
                querySelector: `#${chartId}`
            }).then(() => {
                console.log('图表渲染完成');
            }).catch(error => {
                console.error('图表渲染失败:', error);
                container.innerHTML = '<div class="loading" style="color: #e74c3c;">图表渲染失败，请刷新页面重试</div>';
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');
            switchTheme('default');
        });
    </script>
</body>
</html>
