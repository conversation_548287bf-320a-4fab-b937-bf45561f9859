{"name": "augment-chat-exporter", "version": "1.0.0", "description": "Augment聊天记录导出工具 - 可执行文件版本", "main": "augment-chat-exporter.js", "scripts": {"start": "node augment-chat-exporter.js", "build": "pkg . --out-path=dist", "build-win": "pkg . --targets=node18-win-x64 --out-path=dist", "build-encrypted": "pkg . --targets=node18-win-x64 --out-path=dist && node encrypt-exe.js", "test": "node augment-chat-exporter.js --no-dedup"}, "bin": {"augment-exporter": "./augment-chat-exporter.js"}, "pkg": {"targets": ["node18-win-x64"], "outputPath": "dist", "assets": ["node_modules/level/**/*"]}, "keywords": ["augment", "chat", "export", "vscode", "conversation"], "author": "Your Name", "license": "MIT", "dependencies": {"level": "^8.0.0"}, "devDependencies": {"pkg": "^5.8.1"}}