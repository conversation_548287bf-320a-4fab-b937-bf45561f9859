<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作时间安排 - 甘特图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .gantt-wrapper {
            padding: 20px;
            overflow-x: auto;
        }
        
        .gantt-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1000px;
            font-size: 14px;
        }
        
        .gantt-header {
            background: #1e3a8a;
            color: white;
            font-weight: bold;
        }
        
        .gantt-header th {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #1e40af;
            font-size: 13px;
            white-space: nowrap;
        }
        
        .gantt-header th:first-child {
            width: 180px;
            background: #1e40af;
        }
        
        .gantt-header th:not(:first-child) {
            width: 80px;
        }
        
        .gantt-row {
            border-bottom: 1px solid #e5e7eb;
            height: 50px;
        }
        
        .gantt-row:nth-child(even) {
            background: #f8f9fa;
        }
        
        .gantt-row:nth-child(odd) {
            background: #ffffff;
        }
        
        .task-name {
            padding: 12px 15px;
            font-weight: 500;
            background: #f3f4f6;
            border-right: 2px solid #d1d5db;
            color: #374151;
            vertical-align: middle;
            font-size: 13px;
        }
        
        .month-cell {
            position: relative;
            height: 50px;
            border: 1px solid #e5e7eb;
            padding: 3px;
            vertical-align: middle;
        }
        
        .task-bar-container {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }
        
        .task-bar {
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .task-bar.done {
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
        }
        
        .task-bar.active {
            background: linear-gradient(90deg, #1e40af 60%, #93c5fd 100%);
        }
        
        .task-bar.pending {
            background: linear-gradient(90deg, #6b7280 0%, #d1d5db 100%);
        }
        
        .task-bar.critical {
            background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .progress-text {
            color: white;
            font-size: 11px;
            font-weight: bold;
            padding: 0 8px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            white-space: nowrap;
        }
        
        .milestone {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            z-index: 10;
        }
        
        .milestone.critical {
            background: #dc2626;
        }
        
        .section-divider {
            background: #e2e8f0 !important;
            font-weight: bold;
            color: #475569;
            text-align: center;
            font-size: 12px;
        }
        
        .legend {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: center;
            gap: 25px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
        }
        
        .legend-color {
            width: 18px;
            height: 10px;
            border-radius: 5px;
        }
        
        .legend-color.done { background: linear-gradient(90deg, #1e40af, #3b82f6); }
        .legend-color.active { background: linear-gradient(90deg, #1e40af, #93c5fd); }
        .legend-color.pending { background: linear-gradient(90deg, #6b7280, #d1d5db); }
        .legend-color.critical { background: linear-gradient(90deg, #dc2626, #ef4444); }
        .legend-color.milestone { 
            background: #3b82f6; 
            border-radius: 50%; 
            width: 10px; 
            height: 10px; 
        }
        
        @media (max-width: 768px) {
            .gantt-wrapper {
                padding: 10px;
            }
            
            .task-name {
                font-size: 11px;
                padding: 8px 10px;
            }
            
            .gantt-header th {
                padding: 8px 4px;
                font-size: 11px;
            }
            
            .progress-text {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 工作时间安排</h1>
            <p>2025年度项目进度甘特图</p>
        </div>
        
        <div class="gantt-wrapper">
            <table class="gantt-table">
                <thead>
                    <tr class="gantt-header">
                        <th>工作任务</th>
                        <th>1月</th>
                        <th>2月</th>
                        <th>3月</th>
                        <th>4月</th>
                        <th>5月</th>
                        <th>6月</th>
                        <th>7月</th>
                        <th>8月</th>
                        <th>9月</th>
                        <th>10月</th>
                        <th>11月</th>
                        <th>12月</th>
                    </tr>
                </thead>
                <tbody id="gantt-body">
                    <!-- 动态生成内容 -->
                </tbody>
            </table>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color done"></div>
                <span>已完成</span>
            </div>
            <div class="legend-item">
                <div class="legend-color active"></div>
                <span>进行中</span>
            </div>
            <div class="legend-item">
                <div class="legend-color pending"></div>
                <span>待开始</span>
            </div>
            <div class="legend-item">
                <div class="legend-color critical"></div>
                <span>关键任务</span>
            </div>
            <div class="legend-item">
                <div class="legend-color milestone"></div>
                <span>里程碑</span>
            </div>
        </div>
    </div>

    <script>
        // 基于你的甘特图数据的任务配置
        const taskSections = [
            {
                name: '基础建设',
                tasks: [
                    {
                        name: '后端基础框架搭建',
                        status: 'done',
                        startDate: '2025-08-01',
                        duration: 30,
                        progress: '项目进度100%'
                    },
                    {
                        name: '前端基础框架融合',
                        status: 'active',
                        startDate: '2025-08-10',
                        duration: 45,
                        progress: '项目进度50%'
                    }
                ]
            },
            {
                name: '能力与服务',
                tasks: [
                    {
                        name: '知识库管理',
                        status: 'active',
                        startDate: '2025-08-15',
                        duration: 40,
                        progress: '项目进度50%'
                    },
                    {
                        name: 'MCP服务管理',
                        status: 'pending',
                        startDate: '2025-10-01',
                        duration: 40,
                        progress: '项目进度50%'
                    },
                    {
                        name: '提示词模板管理',
                        status: 'pending',
                        startDate: '2025-10-20',
                        duration: 30,
                        progress: '项目进度50%'
                    }
                ]
            },
            {
                name: '智能体与交互',
                tasks: [
                    {
                        name: '智能体配置',
                        status: 'pending',
                        startDate: '2025-09-01',
                        duration: 45,
                        progress: '项目进度50%'
                    },
                    {
                        name: '多智能体协作编排',
                        status: 'critical',
                        startDate: '2025-11-01',
                        duration: 40,
                        progress: '项目进度50%'
                    },
                    {
                        name: 'Live2D数字人交互',
                        status: 'pending',
                        startDate: '2025-12-01',
                        duration: 45,
                        progress: '项目进度50%'
                    }
                ]
            }
        ];

        // 里程碑数据
        const milestones = [
            { name: '目标 50%（评审）', date: '2026-01-15', type: 'normal' },
            { name: '目标 100%（上线）', date: '2026-03-01', type: 'critical' }
        ];

        // 日期计算函数
        function dateToMonth(dateStr) {
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            
            if (year === 2025) {
                return month;
            } else if (year === 2026) {
                return month + 12;
            }
            return month;
        }

        function daysToMonths(days) {
            return days / 30; // 简化计算，30天为一个月
        }

        function generateGanttChart() {
            const tbody = document.getElementById('gantt-body');
            
            taskSections.forEach(section => {
                // 添加分组标题行
                const sectionRow = document.createElement('tr');
                const sectionCell = document.createElement('td');
                sectionCell.colSpan = 13;
                sectionCell.className = 'section-divider';
                sectionCell.textContent = section.name;
                sectionRow.appendChild(sectionCell);
                tbody.appendChild(sectionRow);
                
                // 添加任务行
                section.tasks.forEach(task => {
                    const row = document.createElement('tr');
                    row.className = 'gantt-row';
                    
                    // 任务名称列
                    const nameCell = document.createElement('td');
                    nameCell.className = 'task-name';
                    nameCell.textContent = task.name;
                    row.appendChild(nameCell);
                    
                    const startMonth = dateToMonth(task.startDate);
                    const durationMonths = daysToMonths(task.duration);
                    
                    // 12个月份列
                    for (let month = 1; month <= 12; month++) {
                        const monthCell = document.createElement('td');
                        monthCell.className = 'month-cell';
                        
                        const container = document.createElement('div');
                        container.className = 'task-bar-container';
                        
                        // 检查任务是否在这个月
                        if (month >= startMonth && month < startMonth + durationMonths) {
                            const taskBar = document.createElement('div');
                            taskBar.className = `task-bar ${task.status}`;
                            
                            // 计算任务条的宽度和位置
                            const startOffset = Math.max(0, (startMonth - month)) * 100;
                            const endOffset = Math.min(1, (startMonth + durationMonths - month)) * 100;
                            const width = Math.max(0, endOffset - startOffset);
                            
                            if (width > 0) {
                                taskBar.style.width = `${width * 100}%`;
                                taskBar.style.marginLeft = `${Math.max(0, startOffset)}%`;
                                
                                // 只在任务开始的月份显示进度文字
                                if (month === Math.ceil(startMonth)) {
                                    const progressText = document.createElement('span');
                                    progressText.className = 'progress-text';
                                    progressText.textContent = task.progress;
                                    taskBar.appendChild(progressText);
                                }
                                
                                container.appendChild(taskBar);
                            }
                        }
                        
                        monthCell.appendChild(container);
                        row.appendChild(monthCell);
                    }
                    
                    tbody.appendChild(row);
                });
            });
            
            // 添加里程碑到相应的月份
            milestones.forEach(milestone => {
                const milestoneMonth = dateToMonth(milestone.date);
                if (milestoneMonth <= 12) {
                    const rows = tbody.querySelectorAll('.gantt-row');
                    if (rows.length > 0) {
                        const lastRow = rows[rows.length - 1];
                        const monthCell = lastRow.children[milestoneMonth];
                        if (monthCell) {
                            const milestoneEl = document.createElement('div');
                            milestoneEl.className = `milestone ${milestone.type}`;
                            milestoneEl.title = milestone.name;
                            milestoneEl.style.right = '10px';
                            monthCell.appendChild(milestoneEl);
                        }
                    }
                }
            });
        }

        // 页面加载完成后生成甘特图
        document.addEventListener('DOMContentLoaded', function() {
            generateGanttChart();
        });
    </script>
</body>
</html>
