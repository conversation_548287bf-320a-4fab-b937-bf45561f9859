<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图 - 简化版</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .chart-area {
            padding: 40px;
        }
        .mermaid {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .theme-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .theme-btn {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .theme-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-default { background: #3498db; color: white; }
        .btn-dark { background: #2c3e50; color: white; }
        .btn-forest { background: #27ae60; color: white; }
        .btn-purple { background: #8b5cf6; color: white; }
        .btn-orange { background: #f97316; color: white; }
        .btn-cyan { background: #06b6d4; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 2025年度工作安排</h1>
        </div>
        
        <div class="theme-buttons">
            <button class="theme-btn btn-default" onclick="changeTheme('default')">经典蓝</button>
            <button class="theme-btn btn-dark" onclick="changeTheme('dark')">深色</button>
            <button class="theme-btn btn-forest" onclick="changeTheme('forest')">森林绿</button>
            <button class="theme-btn btn-purple" onclick="changeTheme('purple')">渐变紫</button>
            <button class="theme-btn btn-orange" onclick="changeTheme('orange')">温暖橙</button>
            <button class="theme-btn btn-cyan" onclick="changeTheme('cyan')">清新青</button>
        </div>
        
        <div class="chart-area">
            <div class="mermaid" id="gantt">
                <!-- 甘特图内容将通过 JavaScript 动态加载 -->
            </div>
        </div>
    </div>

    <script>
        const ganttContent = `gantt
    title 工作时间安排
    dateFormat  YYYY-MM-DD
    axisFormat  %m月
    excludes    weekends

    section 基础建设
    后端基础框架搭建      :done,    backend,  2025-08-01, 30d
    前端基础框架融合      :active,  frontend, 2025-08-10, 45d

    section 能力与服务
    知识库管理            :active,  kb,       2025-08-15, 40d
    MCP服务管理           :         mcp,      2025-10-01, 40d
    提示词模板管理        :         prompt,   2025-10-20, 30d

    section 智能体与交互
    智能体配置            :         aicfg,    2025-09-01, 45d
    多智能体协作编排      :crit,    multi,    2025-11-01, 40d
    Live2D数字人交互      :         live2d,   2025-12-01, 45d

    section 里程碑
    目标 50%（评审） :milestone, m1, 2026-01-15, 0d
    目标 100%（上线）:milestone, m2, 2026-03-01, 0d`;

        const themes = {
            default: { theme: 'default' },
            dark: { theme: 'dark' },
            forest: { theme: 'forest' },
            purple: {
                theme: 'base',
                themeVariables: {
                    primaryColor: '#8B5CF6',
                    primaryTextColor: '#ffffff',
                    primaryBorderColor: '#7C3AED',
                    lineColor: '#4C1D95',
                    sectionBkgColor: '#F3E8FF',
                    altSectionBkgColor: '#E9D5FF',
                    gridColor: '#C4B5FD'
                }
            },
            orange: {
                theme: 'base',
                themeVariables: {
                    primaryColor: '#F97316',
                    primaryTextColor: '#ffffff',
                    primaryBorderColor: '#EA580C',
                    lineColor: '#9A3412',
                    sectionBkgColor: '#FFF7ED',
                    altSectionBkgColor: '#FFEDD5',
                    gridColor: '#FED7AA'
                }
            },
            cyan: {
                theme: 'base',
                themeVariables: {
                    primaryColor: '#06B6D4',
                    primaryTextColor: '#ffffff',
                    primaryBorderColor: '#0891B2',
                    lineColor: '#164E63',
                    sectionBkgColor: '#F0F9FF',
                    altSectionBkgColor: '#E0F7FA',
                    gridColor: '#B2EBF2'
                }
            }
        };

        function changeTheme(themeName) {
            const config = themes[themeName];

            // 清除现有内容
            const element = document.getElementById('gantt');
            element.innerHTML = ganttContent;
            element.removeAttribute('data-processed');

            // 重新初始化 Mermaid
            mermaid.initialize({
                startOnLoad: false,
                ...config
            });

            // 渲染图表
            mermaid.run();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化并显示默认主题的甘特图
            changeTheme('default');
        });
    </script>
</body>
</html>
