# 📊 甘特图美化项目

这是一个基于 Mermaid.js 的甘特图美化方案，提供多种主题和交互功能。

## 🎨 功能特性

- **7种精美主题**：经典蓝色、深色、森林绿、中性灰、渐变紫、温暖橙、清新青
- **响应式设计**：适配各种屏幕尺寸
- **主题切换**：实时切换主题，自动保存用户偏好
- **交互功能**：支持键盘快捷键切换主题
- **现代UI**：毛玻璃效果、渐变背景、动画过渡
- **图例说明**：清晰的任务状态说明

## 📁 文件结构

```
甘特图美化/
├── index.html      # 主页面文件
├── styles.css      # 样式文件
├── script.js       # JavaScript 功能文件
├── mermaid.md      # 原始甘特图数据
└── README.md       # 说明文档
```

## 🚀 使用方法

### 方法一：直接打开
1. 双击 `index.html` 文件
2. 在浏览器中查看美化后的甘特图

### 方法二：本地服务器
```bash
# 使用 Python 启动本地服务器
python -m http.server 8000

# 或使用 Node.js
npx serve .

# 然后访问 http://localhost:8000
```

### 方法三：VSCode Live Server
1. 安装 Live Server 插件
2. 右键 `index.html` 选择 "Open with Live Server"

## 🎯 主题说明

| 主题名称 | 描述 | 适用场景 |
|---------|------|----------|
| 经典蓝色 | Mermaid 默认主题 | 正式文档、商务报告 |
| 深色主题 | 深色背景，护眼模式 | 夜间工作、演示 |
| 森林绿 | 绿色系，自然清新 | 环保项目、户外主题 |
| 中性灰 | 简洁灰色系 | 极简风格、打印友好 |
| 渐变紫 | 紫色渐变，现代感 | 科技项目、创意设计 |
| 温暖橙 | 橙色系，活力四射 | 营销活动、团队建设 |
| 清新青 | 青色系，清爽宜人 | 医疗健康、清洁能源 |

## ⌨️ 快捷键

- `Ctrl/Cmd + 1`：切换到经典蓝色主题
- `Ctrl/Cmd + 2`：切换到深色主题
- `Ctrl/Cmd + 3`：切换到森林绿主题

## 🔧 自定义配置

### 修改甘特图内容
编辑 `script.js` 中的 `ganttContent` 变量：

```javascript
const ganttContent = `
gantt
    title 你的项目标题
    dateFormat  YYYY-MM-DD
    axisFormat  %m月
    
    section 你的分组
    任务名称    :状态, 任务ID, 开始日期, 持续时间
`;
```

### 添加新主题
在 `script.js` 的 `themes` 对象中添加新主题：

```javascript
customNew: {
    theme: 'base',
    themeVariables: {
        primaryColor: '#你的颜色',
        primaryTextColor: '#ffffff',
        // 更多配置...
    }
}
```

## 📱 响应式支持

- **桌面端**：完整功能，最佳体验
- **平板端**：自适应布局，触摸友好
- **手机端**：垂直布局，简化操作

## 🌐 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 📝 更新日志

### v1.0.0 (2025-01-27)
- 初始版本发布
- 支持7种主题
- 响应式设计
- 主题切换功能

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License - 可自由使用和修改
