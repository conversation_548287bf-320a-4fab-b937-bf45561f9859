<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作时间安排 - 自定义甘特图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .gantt-container {
            padding: 20px;
            overflow-x: auto;
        }
        
        .gantt-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        
        .gantt-header {
            background: #1e3a8a;
            color: white;
            font-weight: bold;
        }
        
        .gantt-header th {
            padding: 15px 10px;
            text-align: center;
            border: 1px solid #1e40af;
            font-size: 14px;
        }
        
        .gantt-header th:first-child {
            width: 150px;
            background: #1e40af;
        }
        
        .gantt-row {
            border-bottom: 1px solid #e5e7eb;
        }
        
        .gantt-row:nth-child(even) {
            background: #f9fafb;
        }
        
        .gantt-row:nth-child(odd) {
            background: #ffffff;
        }
        
        .task-name {
            padding: 15px;
            font-weight: 500;
            background: #f3f4f6;
            border-right: 2px solid #d1d5db;
            color: #374151;
        }
        
        .month-cell {
            position: relative;
            height: 50px;
            border: 1px solid #e5e7eb;
            padding: 5px;
        }
        
        .task-bar {
            position: relative;
            height: 30px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .task-bar.done {
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
        }
        
        .task-bar.active {
            background: linear-gradient(90deg, #1e40af 0%, #60a5fa 100%);
        }
        
        .task-bar.pending {
            background: linear-gradient(90deg, #6b7280 0%, #9ca3af 100%);
        }
        
        .task-bar.critical {
            background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
        }
        
        .progress-text {
            color: white;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .milestone {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        .milestone.critical {
            background: #dc2626;
        }
        
        .legend {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 12px;
            border-radius: 6px;
        }
        
        .legend-color.done { background: linear-gradient(90deg, #1e40af, #3b82f6); }
        .legend-color.active { background: linear-gradient(90deg, #1e40af, #60a5fa); }
        .legend-color.pending { background: linear-gradient(90deg, #6b7280, #9ca3af); }
        .legend-color.critical { background: linear-gradient(90deg, #dc2626, #ef4444); }
        .legend-color.milestone { 
            background: #3b82f6; 
            border-radius: 50%; 
            width: 12px; 
            height: 12px; 
        }
        
        @media (max-width: 768px) {
            .gantt-container {
                padding: 10px;
            }
            
            .task-name {
                font-size: 12px;
                padding: 10px;
            }
            
            .gantt-header th {
                padding: 10px 5px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 工作时间安排</h1>
            <p>2025年度项目进度甘特图</p>
        </div>
        
        <div class="gantt-container">
            <table class="gantt-table">
                <thead>
                    <tr class="gantt-header">
                        <th>工作任务</th>
                        <th>1月</th>
                        <th>2月</th>
                        <th>3月</th>
                        <th>4月</th>
                        <th>5月</th>
                        <th>6月</th>
                        <th>7月</th>
                        <th>8月</th>
                        <th>9月</th>
                        <th>10月</th>
                        <th>11月</th>
                        <th>12月</th>
                    </tr>
                </thead>
                <tbody id="gantt-body">
                    <!-- 动态生成内容 -->
                </tbody>
            </table>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color done"></div>
                <span>已完成</span>
            </div>
            <div class="legend-item">
                <div class="legend-color active"></div>
                <span>进行中</span>
            </div>
            <div class="legend-item">
                <div class="legend-color pending"></div>
                <span>待开始</span>
            </div>
            <div class="legend-item">
                <div class="legend-color critical"></div>
                <span>关键任务</span>
            </div>
            <div class="legend-item">
                <div class="legend-color milestone"></div>
                <span>里程碑</span>
            </div>
        </div>
    </div>

    <script>
        // 根据你的甘特图数据计算任务信息
        const tasks = [
            {
                name: '后端基础框架搭建',
                status: 'done',
                startMonth: 8,
                duration: 1,
                progress: '项目进度100%',
                section: '基础建设'
            },
            {
                name: '前端基础框架融合',
                status: 'active',
                startMonth: 8.3,
                duration: 1.5,
                progress: '项目进度50%',
                section: '基础建设'
            },
            {
                name: '知识库管理',
                status: 'active',
                startMonth: 8.5,
                duration: 1.3,
                progress: '项目进度50%',
                section: '能力与服务'
            },
            {
                name: 'MCP服务管理',
                status: 'pending',
                startMonth: 10,
                duration: 1.3,
                progress: '项目进度50%',
                section: '能力与服务'
            },
            {
                name: '提示词模板管理',
                status: 'pending',
                startMonth: 10.7,
                duration: 1,
                progress: '项目进度50%',
                section: '能力与服务'
            },
            {
                name: '智能体配置',
                status: 'pending',
                startMonth: 9,
                duration: 1.5,
                progress: '项目进度50%',
                section: '智能体与交互'
            },
            {
                name: '多智能体协作编排',
                status: 'critical',
                startMonth: 11,
                duration: 1.3,
                progress: '项目进度50%',
                section: '智能体与交互'
            },
            {
                name: 'Live2D数字人交互',
                status: 'pending',
                startMonth: 12,
                duration: 1.5,
                progress: '项目进度50%',
                section: '智能体与交互'
            }
        ];

        // 里程碑数据
        const milestones = [
            { name: '目标 50%（评审）', month: 1, type: 'normal' },
            { name: '目标 100%（上线）', month: 3, type: 'critical' }
        ];

        function generateGanttChart() {
            const tbody = document.getElementById('gantt-body');
            
            tasks.forEach(task => {
                const row = document.createElement('tr');
                row.className = 'gantt-row';
                
                // 任务名称列
                const nameCell = document.createElement('td');
                nameCell.className = 'task-name';
                nameCell.textContent = task.name;
                row.appendChild(nameCell);
                
                // 12个月份列
                for (let month = 1; month <= 12; month++) {
                    const monthCell = document.createElement('td');
                    monthCell.className = 'month-cell';
                    
                    // 检查任务是否在这个月
                    if (month >= task.startMonth && month < task.startMonth + task.duration) {
                        const taskBar = document.createElement('div');
                        taskBar.className = `task-bar ${task.status}`;
                        
                        // 计算任务条的宽度和位置
                        const startOffset = Math.max(0, task.startMonth - month) * 100;
                        const endOffset = Math.min(1, task.startMonth + task.duration - month) * 100;
                        const width = endOffset - startOffset;
                        
                        taskBar.style.width = `${width}%`;
                        taskBar.style.marginLeft = `${startOffset}%`;
                        
                        // 只在第一个月显示进度文字
                        if (month === Math.ceil(task.startMonth)) {
                            const progressText = document.createElement('span');
                            progressText.className = 'progress-text';
                            progressText.textContent = task.progress;
                            taskBar.appendChild(progressText);
                        }
                        
                        monthCell.appendChild(taskBar);
                    }
                    
                    // 添加里程碑
                    milestones.forEach(milestone => {
                        if (month === milestone.month) {
                            const milestoneEl = document.createElement('div');
                            milestoneEl.className = `milestone ${milestone.type}`;
                            milestoneEl.title = milestone.name;
                            monthCell.appendChild(milestoneEl);
                        }
                    });
                    
                    row.appendChild(monthCell);
                }
                
                tbody.appendChild(row);
            });
        }

        // 页面加载完成后生成甘特图
        document.addEventListener('DOMContentLoaded', function() {
            generateGanttChart();
        });
    </script>
</body>
</html>
