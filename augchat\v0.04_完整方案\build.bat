@echo off
chcp 65001 >nul
echo ========================================
echo   Augment 聊天记录导出器 构建脚本
echo ========================================
echo.

:: 检查 Node.js
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 未检测到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 环境检测通过
echo.

:: 选择构建模式
echo 选择构建模式:
echo [1] 基础构建 (快速，适合测试)
echo [2] 高级构建 (加密保护，适合分享)
echo.
set /p mode="请选择 (1 或 2): "

if "%mode%"=="2" (
    echo.
    echo 🔐 启动高级加密构建...
    node advanced-build.js
) else (
    echo.
    echo 🚀 启动基础构建...
    node build-exe.js
)

if %errorLevel% equ 0 (
    echo.
    echo 🎉 构建完成！
    echo 📁 输出目录: dist/
    echo.
    echo 是否打开输出目录? (Y/N)
    set /p open="请选择: "
    if /i "%open%"=="Y" (
        explorer dist
    )
) else (
    echo.
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause
