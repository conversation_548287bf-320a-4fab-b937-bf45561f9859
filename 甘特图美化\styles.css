/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.theme-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.theme-selector label {
    font-size: 1.1rem;
    font-weight: 500;
}

.theme-selector select {
    padding: 10px 15px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-selector select:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.theme-selector select option {
    background: #2c3e50;
    color: white;
}

/* 图表容器 */
.chart-container {
    padding: 40px;
    background: white;
}

#gantt-chart {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 信息面板 */
.info-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-top: 1px solid #dee2e6;
}

.info-panel h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 500;
}

.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.legend-item:hover {
    transform: translateY(-2px);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
}

.legend-color.done {
    background: #28a745;
}

.legend-color.active {
    background: #007bff;
}

.legend-color.pending {
    background: #6c757d;
}

.legend-color.critical {
    background: #dc3545;
}

.legend-color.milestone {
    background: #ffc107;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .chart-container {
        padding: 20px;
    }
    
    .legend {
        justify-content: center;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container {
    animation: fadeIn 0.8s ease-out;
}

/* Mermaid 图表自定义样式 */
.mermaid {
    font-family: 'Microsoft YaHei', sans-serif !important;
}

/* 确保甘特图时间轴标签显示 */
.mermaid .tick text {
    font-family: 'Microsoft YaHei', sans-serif !important;
    font-size: 12px !important;
    fill: #333 !important;
}

.mermaid .domain {
    stroke: #333 !important;
}

.mermaid .grid .tick line {
    stroke: #e0e0e0 !important;
}

/* 甘特图特定样式 */
.mermaid .gantt .grid .tick text {
    font-size: 12px !important;
    font-family: 'Microsoft YaHei', sans-serif !important;
    fill: #666 !important;
}

.mermaid .gantt .grid .tick line {
    stroke: #ddd !important;
}

.mermaid .gantt .today {
    stroke: #ff6b6b !important;
    stroke-width: 2px !important;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
}
