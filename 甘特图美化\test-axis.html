<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图时间轴测试</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .mermaid {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        /* 强制显示时间轴标签 */
        .mermaid .tick text {
            font-family: 'Microsoft YaHei', sans-serif !important;
            font-size: 12px !important;
            fill: #333 !important;
            display: block !important;
            visibility: visible !important;
        }
        .mermaid .domain {
            stroke: #333 !important;
            stroke-width: 1px !important;
        }
        .mermaid .grid .tick line {
            stroke: #e0e0e0 !important;
            stroke-width: 1px !important;
        }
        .mermaid .gantt .grid .tick text {
            font-size: 12px !important;
            font-family: 'Microsoft YaHei', sans-serif !important;
            fill: #666 !important;
            display: block !important;
            visibility: visible !important;
        }
        .status {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图时间轴显示测试</h1>
        <div class="status" id="status">正在加载...</div>
        
        <h2>测试1: 基本甘特图</h2>
        <div class="mermaid" id="chart1">
gantt
    title 2025年工作计划
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m
    
    section 测试
    任务1 :done, task1, 2025-01-01, 30d
    任务2 :active, task2, 2025-02-01, 45d
    任务3 :task3, 2025-03-15, 30d
        </div>
        
        <h2>测试2: 月份格式</h2>
        <div class="mermaid" id="chart2">
gantt
    title 2025年工作计划（月份格式）
    dateFormat YYYY-MM-DD
    axisFormat %m月
    
    section 测试
    任务A :done, taskA, 2025-01-01, 60d
    任务B :active, taskB, 2025-03-01, 90d
    任务C :taskC, 2025-06-01, 60d
        </div>
        
        <button onclick="reinitialize()">重新初始化</button>
        <button onclick="checkElements()">检查元素</button>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }

        function reinitialize() {
            updateStatus('重新初始化中...');
            
            // 清除所有图表
            document.querySelectorAll('.mermaid').forEach(el => {
                if (el.id) {
                    el.removeAttribute('data-processed');
                }
            });
            
            // 重新初始化
            mermaid.initialize({
                startOnLoad: false,
                theme: 'default',
                gantt: {
                    fontSize: 12,
                    fontFamily: 'Microsoft YaHei, sans-serif',
                    axisFormat: '%Y-%m',
                    bottomPadding: 50,
                    rightPadding: 100,
                    leftPadding: 100,
                    topPadding: 50
                }
            });
            
            // 渲染图表
            mermaid.run().then(() => {
                updateStatus('重新初始化完成');
                setTimeout(checkElements, 1000);
            }).catch(error => {
                updateStatus('初始化失败: ' + error.message);
            });
        }

        function checkElements() {
            const ticks = document.querySelectorAll('.mermaid .tick text');
            const domains = document.querySelectorAll('.mermaid .domain');
            const gridLines = document.querySelectorAll('.mermaid .grid .tick line');
            
            updateStatus(`找到 ${ticks.length} 个时间标签, ${domains.length} 个坐标轴, ${gridLines.length} 个网格线`);
            
            ticks.forEach((tick, index) => {
                console.log(`时间标签 ${index}:`, tick.textContent, tick.style.display, tick.style.visibility);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，开始初始化...');
            
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                gantt: {
                    fontSize: 12,
                    fontFamily: 'Microsoft YaHei, sans-serif',
                    bottomPadding: 50,
                    rightPadding: 100,
                    leftPadding: 100,
                    topPadding: 50
                }
            });
            
            setTimeout(() => {
                updateStatus('初始化完成');
                checkElements();
            }, 2000);
        });
    </script>
</body>
</html>
